# -*- coding: utf-8 -*-
"""
@File  : check_permit_eia_emission_threshold.py
@Author: <PERSON>
@Date  : 2025/9/3 15:25
@Desc  : XKZ-02-1-许可证记载的污染物浓度/年排放量/速率 限值大于环境影响评价报告记载的排放限值
"""

import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime
import traceback

from lib import logger
from lib.check.base import get_db_cursor, timing_decorator
from lib.check.table_name_generator import get_table_name


def get_city_code(city: str) -> str:
    """
    根据城市名称获取城市编码

    Args:
        city (str): 城市名称

    Returns:
        str: 城市编码
    """
    db = get_db_cursor('main')
    try:
        sql = """
            SELECT city_code
            FROM dim_ref_comm_prov_city
            WHERE city_name = %s
        """
        result = db.query_sql(sql % (f"'{city}'",))

        if not result.empty:
            return result.iloc[0]['city_code']
        else:
            raise ValueError(f"未找到城市 {city} 的编码")
    finally:
        db.close()


def check_total_emission_clues(city: str, city_code: str, start_date: str, end_date: str) -> List[Dict]:
    """
    检查全厂排放口总计线索

    Args:
        city (str): 城市名称
        city_code (str): 城市编码
        start_date (str): 起始时间
        end_date (str): 结束时间

    Returns:
        List[Dict]: 全厂排放口总计线索列表
    """
    db = get_db_cursor('main')
    clues = []

    try:
        # 动态生成表名
        pw_air_total_table = get_table_name("pw_basic_information_air_emission_total_permit", city)
        pw_water_total_table = get_table_name("pw_basic_information_water_pollutant_emission_total", city)
        hp_air_annual_table = get_table_name("hp_basic_data_air_pollutant_annual_emission", city)
        hp_water_annual_table = get_table_name("hp_basic_data_water_pollutant_annual_emission", city)

        # 检查大气全厂排放口总计
        air_sql = f"""
            SELECT
                pw.f_enterprise_id as pw_enterprise_id,
                pw.f_enterprise_name as pw_enterprise_name,
                pw.uscc as pw_uscc,
                pw.pollutant_name as pw_pollutant_name,
                pw.std_code as pw_std_code,
                pw.emission_year_1 as pw_emission_year_1,
                hp.hp_enterprise_id,
                hp.hp_enterprise_name,
                hp.pollutant as hp_pollutant_name,
                hp.std_code as hp_std_code,
                hp.annual_emission as hp_annual_emission,
                hp.uscc as hp_uscc,
                'organized' as pollution_type
            FROM {pw_air_total_table} pw
            INNER JOIN {hp_air_annual_table} hp
                ON pw.f_enterprise_name = hp.hp_enterprise_name
                AND pw.std_code = hp.std_code
            WHERE pw.region_code = '{city_code}'
                AND hp.region_code = '{city_code}'
                AND pw.emission_year_1 IS NOT NULL
                AND hp.annual_emission IS NOT NULL
                AND CAST(pw.emission_year_1 AS NUMERIC) > CAST(hp.annual_emission AS NUMERIC)
        """

        air_result = db.query_sql(air_sql)

        for _, row in air_result.iterrows():
            clue = {
                "f_enterprise_id": row['pw_enterprise_id'],
                "f_enterprise_name": row['pw_enterprise_name'],
                "hp_enterprise_id": row['hp_enterprise_id'],
                "hp_enterprise_name": row['hp_enterprise_name'],
                "pw_pollutant_name": row['pw_pollutant_name'],
                "hp_pollutant_name": row['hp_pollutant_name'],
                "std_code": row['pw_std_code'],
                "uscc": row['pw_uscc'],
                "pollution_type": row['pollution_type'],
                "pw_emission_value": float(row['pw_emission_year_1']),
                "hp_emission_value": float(row['hp_annual_emission'])
            }
            clues.append(clue)

        # 检查水全厂排放口总计
        water_sql = f"""
            SELECT
                pw.f_enterprise_id as pw_enterprise_id,
                pw.f_enterprise_name as pw_enterprise_name,
                pw.uscc as pw_uscc,
                pw.pollutant_name as pw_pollutant_name,
                pw.std_code as pw_std_code,
                pw.emission_year_1 as pw_emission_year_1,
                hp.hp_enterprise_id,
                hp.hp_enterprise_name,
                hp.pollutant as hp_pollutant_name,
                hp.std_code as hp_std_code,
                hp.annual_emission as hp_annual_emission,
                hp.uscc as hp_uscc,
                'water' as pollution_type
            FROM {pw_water_total_table} pw
            INNER JOIN {hp_water_annual_table} hp
                ON pw.f_enterprise_name = hp.hp_enterprise_name
                AND pw.std_code = hp.std_code
            WHERE pw.region_code = '{city_code}'
                AND hp.region_code = '{city_code}'
                AND pw.outlet_type = '全厂排放口总计'
                AND pw.emission_year_1 IS NOT NULL
                AND hp.annual_emission IS NOT NULL
                AND CAST(pw.emission_year_1 AS NUMERIC) > CAST(hp.annual_emission AS NUMERIC)
        """

        water_result = db.query_sql(water_sql)

        for _, row in water_result.iterrows():
            clue = {
                "f_enterprise_id": row['pw_enterprise_id'],
                "f_enterprise_name": row['pw_enterprise_name'],
                "hp_enterprise_id": row['hp_enterprise_id'],
                "hp_enterprise_name": row['hp_enterprise_name'],
                "pw_pollutant_name": row['pw_pollutant_name'],
                "hp_pollutant_name": row['hp_pollutant_name'],
                "std_code": row['pw_std_code'],
                "uscc": row['pw_uscc'],
                "pollution_type": row['pollution_type'],
                "pw_emission_value": float(row['pw_emission_year_1']),
                "hp_emission_value": float(row['hp_annual_emission'])
            }
            clues.append(clue)

    except Exception as e:
        logger.error(f"检查全厂排放口总计线索时发生错误: {e}")
        logger.error(traceback.format_exc())
    finally:
        db.close()

    return clues


def check_concentration_clues(city: str, city_code: str, start_date: str, end_date: str) -> List[Dict]:
    """
    检查排口浓度线索

    Args:
        city (str): 城市名称
        city_code (str): 城市编码
        start_date (str): 起始时间
        end_date (str): 结束时间

    Returns:
        List[Dict]: 排口浓度线索列表
    """
    db = get_db_cursor('main')
    clues = []

    try:
        # 动态生成表名
        mapping_table = get_table_name("pw_basic_map_hp_map_pw_monitor_sit", city)
        pw_air_emission_table = get_table_name("pw_basic_information_organized_emission", city)
        pw_water_emission_table = get_table_name("pw_basic_information_water_pollutant_emission", city)
        hp_air_limit_table = get_table_name("hp_basic_data_air_pollutant_emission_limit", city)
        hp_water_limit_table = get_table_name("hp_basic_data_water_pollutant_emission_limit", city)

        # 检查大气排口浓度
        air_concentration_sql = f"""
            SELECT
                pw.f_enterprise_id as pw_enterprise_id,
                pw.f_enterprise_name as pw_enterprise_name,
                pw.outlet_code as pw_outlet_code,
                pw.outlet_name as pw_outlet_name,
                pw.uscc as pw_uscc,
                pw.pollutant_name as pw_pollutant_name,
                pw.std_code as pw_std_code,
                pw.permit_con_limit as pw_concentration_limit,
                hp.hp_enterprise_id,
                hp.hp_enterprise_name,
                hp.outlet_code as hp_outlet_code,
                hp.outlet_name as hp_outlet_name,
                hp.pollutant as hp_pollutant_name,
                hp.std_code as hp_std_code,
                hp.emission_con_limit as hp_concentration_limit,
                hp.uscc as hp_uscc,
                'organized' as pollution_type
            FROM {mapping_table} map
            INNER JOIN {pw_air_emission_table} pw
                ON map.f_enterprise_id = pw.f_enterprise_id
                AND map.pw_outlet_code = pw.outlet_code
            INNER JOIN {hp_air_limit_table} hp
                ON map.hp_enterprise_id = hp.hp_enterprise_id
                AND map.hp_outlet_code = hp.outlet_code
                AND pw.std_code = hp.std_code
            WHERE map.region_code = '{city_code}'
                AND pw.region_code = '{city_code}'
                AND hp.region_code = '{city_code}'
                AND map.monitoring_type = '废气'
                AND pw.permit_con_limit IS NOT NULL
                AND hp.emission_con_limit IS NOT NULL
                AND CAST(pw.permit_con_limit AS NUMERIC) > CAST(hp.emission_con_limit AS NUMERIC)
        """

        air_concentration_result = db.query_sql(air_concentration_sql)

        for _, row in air_concentration_result.iterrows():
            clue = {
                "f_enterprise_id": row['pw_enterprise_id'],
                "f_enterprise_name": row['pw_enterprise_name'],
                "hp_enterprise_id": row['hp_enterprise_id'],
                "hp_enterprise_name": row['hp_enterprise_name'],
                "outlet_id": row['pw_outlet_code'],
                "pw_outlet_name": row['pw_outlet_name'],
                "hp_outlet_name": row['hp_outlet_name'],
                "pw_pollutant_name": row['pw_pollutant_name'],
                "hp_pollutant_name": row['hp_pollutant_name'],
                "std_code": row['pw_std_code'],
                "uscc": row['pw_uscc'],
                "pollution_type": row['pollution_type'],
                "pw_concentration_limit": float(row['pw_concentration_limit']),
                "hp_concentration_limit": float(row['hp_concentration_limit'])
            }
            clues.append(clue)

        # 检查水排口浓度
        water_concentration_sql = f"""
            SELECT
                pw.f_enterprise_id as pw_enterprise_id,
                pw.f_enterprise_name as pw_enterprise_name,
                pw.outlet_code as pw_outlet_code,
                pw.outlet_name as pw_outlet_name,
                pw.uscc as pw_uscc,
                pw.pollutant_name as pw_pollutant_name,
                pw.std_code as pw_std_code,
                pw.permit_con_up_limit as pw_concentration_limit,
                hp.hp_enterprise_id,
                hp.hp_enterprise_name,
                hp.outlet_code as hp_outlet_code,
                hp.outlet_name as hp_outlet_name,
                hp.pollutant as hp_pollutant_name,
                hp.std_code as hp_std_code,
                hp.emission_con_limit as hp_concentration_limit,
                hp.uscc as hp_uscc,
                'water' as pollution_type
            FROM {mapping_table} map
            INNER JOIN {pw_water_emission_table} pw
                ON map.f_enterprise_id = pw.f_enterprise_id
                AND map.pw_outlet_code = pw.outlet_code
            INNER JOIN {hp_water_limit_table} hp
                ON map.hp_enterprise_id = hp.hp_enterprise_id
                AND map.hp_outlet_code = hp.outlet_code
                AND pw.std_code = hp.std_code
            WHERE map.region_code = '{city_code}'
                AND pw.region_code = '{city_code}'
                AND hp.region_code = '{city_code}'
                AND map.monitoring_type = '废水'
                AND pw.permit_con_up_limit IS NOT NULL
                AND hp.emission_con_limit IS NOT NULL
                AND CAST(pw.permit_con_up_limit AS NUMERIC) > CAST(hp.emission_con_limit AS NUMERIC)
        """

        water_concentration_result = db.query_sql(water_concentration_sql)

        for _, row in water_concentration_result.iterrows():
            clue = {
                "f_enterprise_id": row['pw_enterprise_id'],
                "f_enterprise_name": row['pw_enterprise_name'],
                "hp_enterprise_id": row['hp_enterprise_id'],
                "hp_enterprise_name": row['hp_enterprise_name'],
                "outlet_id": row['pw_outlet_code'],
                "pw_outlet_name": row['pw_outlet_name'],
                "hp_outlet_name": row['hp_outlet_name'],
                "pw_pollutant_name": row['pw_pollutant_name'],
                "hp_pollutant_name": row['hp_pollutant_name'],
                "std_code": row['pw_std_code'],
                "uscc": row['pw_uscc'],
                "pollution_type": row['pollution_type'],
                "pw_concentration_limit": float(row['pw_concentration_limit']),
                "hp_concentration_limit": float(row['hp_concentration_limit'])
            }
            clues.append(clue)

    except Exception as e:
        logger.error(f"检查排口浓度线索时发生错误: {e}")
        logger.error(traceback.format_exc())
    finally:
        db.close()

    return clues


def check_emission_rate_clues(city: str, city_code: str, start_date: str, end_date: str) -> List[Dict]:
    """
    检查排口速率线索

    Args:
        city (str): 城市名称
        city_code (str): 城市编码
        start_date (str): 起始时间
        end_date (str): 结束时间

    Returns:
        List[Dict]: 排口速率线索列表
    """
    db = get_db_cursor('main')
    clues = []

    try:
        # 动态生成表名
        mapping_table = get_table_name("pw_basic_map_hp_map_pw_monitor_sit", city)
        pw_air_emission_table = get_table_name("pw_basic_information_organized_emission", city)
        hp_air_limit_table = get_table_name("hp_basic_data_air_pollutant_emission_limit", city)
        hp_water_limit_table = get_table_name("hp_basic_data_water_pollutant_emission_limit", city)

        # 检查大气排口速率（只有大气有速率限值）
        air_rate_sql = f"""
            SELECT
                pw.f_enterprise_id as pw_enterprise_id,
                pw.f_enterprise_name as pw_enterprise_name,
                pw.outlet_code as pw_outlet_code,
                pw.outlet_name as pw_outlet_name,
                pw.uscc as pw_uscc,
                pw.pollutant_name as pw_pollutant_name,
                pw.std_code as pw_std_code,
                pw.emission_rate_limit as pw_emission_rate_limit,
                hp.hp_enterprise_id,
                hp.hp_enterprise_name,
                hp.outlet_code as hp_outlet_code,
                hp.outlet_name as hp_outlet_name,
                hp.pollutant as hp_pollutant_name,
                hp.std_code as hp_std_code,
                hp.emission_rate_limit as hp_emission_rate_limit,
                hp.uscc as hp_uscc,
                'organized' as pollution_type
            FROM {mapping_table} map
            INNER JOIN {pw_air_emission_table} pw
                ON map.f_enterprise_id = pw.f_enterprise_id
                AND map.pw_outlet_code = pw.outlet_code
            INNER JOIN {hp_air_limit_table} hp
                ON map.hp_enterprise_id = hp.hp_enterprise_id
                AND map.hp_outlet_code = hp.outlet_code
                AND pw.std_code = hp.std_code
            WHERE map.region_code = '{city_code}'
                AND pw.region_code = '{city_code}'
                AND hp.region_code = '{city_code}'
                AND map.monitoring_type = '废气'
                AND pw.emission_rate_limit IS NOT NULL
                AND hp.emission_rate_limit IS NOT NULL
                AND CAST(pw.emission_rate_limit AS NUMERIC) > CAST(hp.emission_rate_limit AS NUMERIC)
        """

        air_rate_result = db.query_sql(air_rate_sql)

        for _, row in air_rate_result.iterrows():
            clue = {
                "f_enterprise_id": row['pw_enterprise_id'],
                "f_enterprise_name": row['pw_enterprise_name'],
                "hp_enterprise_id": row['hp_enterprise_id'],
                "hp_enterprise_name": row['hp_enterprise_name'],
                "outlet_id": row['pw_outlet_code'],
                "pw_outlet_name": row['pw_outlet_name'],
                "hp_outlet_name": row['hp_outlet_name'],
                "pw_pollutant_name": row['pw_pollutant_name'],
                "hp_pollutant_name": row['hp_pollutant_name'],
                "std_code": row['pw_std_code'],
                "uscc": row['pw_uscc'],
                "pollution_type": row['pollution_type'],
                "pw_emission_rate_limit": float(row['pw_emission_rate_limit']),
                "hp_emission_rate_limit": float(row['hp_emission_rate_limit'])
            }
            clues.append(clue)

    except Exception as e:
        logger.error(f"检查排口速率线索时发生错误: {e}")
        logger.error(traceback.format_exc())
    finally:
        db.close()

    return clues


@timing_decorator
def check_permit_eia_emission_threshold(city: str, start_date: str, end_date: str) -> Dict[str, List[Dict]]:
    """
    检查许可证记载的污染物全厂排放口总计或排口浓度或排口速率大于环评记载的排放限值线索

    Args:
        city (str): 城市名称
        start_date (str): 起始时间，格式：'YYYY-MM-DD'
        end_date (str): 结束时间，格式：'YYYY-MM-DD'

    Returns:
        Dict[str, List[Dict]]: 包含三种线索的字典
        {
            "total_emission_clues": [...],  # 全厂排放口总计线索
            "concentration_clues": [...],   # 排口浓度线索
            "emission_rate_clues": [...]    # 排口速率线索
        }
    """
    logger.info(f'开始检查许可证与环评排放限值比较 - 城市: {city}, 时间范围: {start_date} 到 {end_date}')

    try:
        # 获取城市编码
        city_code = get_city_code(city)
        logger.info(f'城市编码: {city_code}')

        # 检查三种类型的线索
        total_emission_clues = check_total_emission_clues(city, city_code, start_date, end_date)
        concentration_clues = check_concentration_clues(city, city_code, start_date, end_date)
        emission_rate_clues = check_emission_rate_clues(city, city_code, start_date, end_date)

        result = {
            "total_emission_clues": total_emission_clues,
            "concentration_clues": concentration_clues,
            "emission_rate_clues": emission_rate_clues
        }

        logger.info(f'检查完成 - 全厂排放口总计线索: {len(total_emission_clues)}条, '
                   f'排口浓度线索: {len(concentration_clues)}条, '
                   f'排口速率线索: {len(emission_rate_clues)}条')

        return result

    except Exception as e:
        logger.error(f"检查许可证与环评排放限值比较时发生错误: {e}")
        logger.error(traceback.format_exc())
        return {
            "total_emission_clues": [],
            "concentration_clues": [],
            "emission_rate_clues": []
        }


def run(city: str, start_date: str, end_date: str) -> Dict[str, List[Dict]]:
    """
    主函数：执行许可证与环评排放限值比较检查

    Args:
        city (str): 城市名称
        start_date (str): 起始时间，格式：'YYYY-MM-DD'
        end_date (str): 结束时间，格式：'YYYY-MM-DD'

    Returns:
        Dict[str, List[Dict]]: 检查结果
    """
    return check_permit_eia_emission_threshold(city, start_date, end_date)


"""
返回示例及字段说明
{
  "status": "OK",
  "data": {
    "total_emission_clues": [  // 全厂排放口总计线索列表
      {
        // === 基础标识信息 ===
        "f_enterprise_id": "646332592201797",         // 许可证企业ID
        "f_enterprise_name": "西南铝业（集团）有限责任公司",  // 许可证企业名称
        "hp_enterprise_id": "11bc459c47236762e0dc1cf408695cb7", // 环评企业ID
        "hp_enterprise_name": "西南铝业（集团）有限责任公司", // 环评企业名称
        "uscc": "915000002028029250",                 // 统一社会信用代码

        // === 污染物信息 ===
        "pw_pollutant_name": "颗粒物",                // 许可证污染物名称
        "hp_pollutant_name": "颗粒物",                // 环评污染物名称
        "std_code": "a34000",                        // 污染物标准编码（统一使用许可证编码）
        "pollution_type": "organized",               // 污染类型：organized(有组织废气)/water(废水)

        // === 排放限值对比 ===
        "pw_emission_value": 48.058,                 // 许可证年排放量限值
        "hp_emission_value": 5.499                  // 环评年排放量限值
      },
      {
        "f_enterprise_id": "646332592201797",
        "f_enterprise_name": "西南铝业（集团）有限责任公司",
        "hp_enterprise_id": "11bc459c47236762e0dc1cf408695cb7",
        "hp_enterprise_name": "西南铝业（集团）有限责任公司",
        "pw_pollutant_name": "氨氮",
        "hp_pollutant_name": "NH3-N",
        "std_code": "w21003",
        "uscc": "915000002028029250",
        "pollution_type": "water",
        "pw_emission_value": 19.56,
        "hp_emission_value": 0.287
      }
    ],
    "concentration_clues": [  // 排口浓度线索列表
      {
        // === 基础标识信息 ===
        "f_enterprise_id": "684569558179909",         // 许可证企业ID
        "f_enterprise_name": "重庆市九龙坡区人民医院",    // 许可证企业名称
        "hp_enterprise_id": "57b7179d87f9217ead169756c315c78c", // 环评企业ID
        "hp_enterprise_name": "重庆市九龙坡区人民医院",   // 环评企业名称
        "uscc": "1250010735871100XD",                // 统一社会信用代码

        // === 排口信息 ===
        "outlet_id": "DA001",                        // 排口ID（统一使用许可证排口ID）
        "pw_outlet_name": "废水处理废气排放口",          // 许可证排口名称
        "hp_outlet_name": "污水处理站臭气排气筒",        // 环评排口名称

        // === 污染物信息 ===
        "pw_pollutant_name": "臭气浓度",              // 许可证污染物名称
        "hp_pollutant_name": "臭气浓度",              // 环评污染物名称
        "std_code": "a19002",                        // 污染物标准编码（统一使用许可证编码）
        "pollution_type": "organized",               // 污染类型：organized(有组织废气)/water(废水)

        // === 浓度限值对比 ===
        "pw_concentration_limit": 2000.0,            // 许可证浓度限值
        "hp_concentration_limit": 10.0               // 环评浓度限值
      },
      {
        "f_enterprise_id": "646332400029765",
        "f_enterprise_name": "重庆志成机械有限公司",
        "hp_enterprise_id": "1ee98dd8f6332153136af0d386dbc0d0",
        "hp_enterprise_name": "重庆志成机械有限公司",
        "outlet_id": "DW001",
        "pw_outlet_name": "污水处理站废水排放口",
        "hp_outlet_name": "DW001",
        "pw_pollutant_name": "化学需氧量",
        "hp_pollutant_name": "COD",
        "std_code": "w01018",
        "uscc": "91500107009293786A",
        "pollution_type": "water",
        "pw_concentration_limit": 500.0,
        "hp_concentration_limit": 100.0
      }
    ],
    "emission_rate_clues": [  // 排口速率线索列表（仅大气污染物）
      {
        // === 基础标识信息 ===
        "f_enterprise_id": "646334136856645",         // 许可证企业ID
        "f_enterprise_name": "重庆国鸿氢能科技有限公司", // 许可证企业名称
        "hp_enterprise_id": "6241a7144efebbce2402774906cb894a", // 环评企业ID
        "hp_enterprise_name": "重庆国鸿氢能科技有限公司", // 环评企业名称
        "uscc": "91500000MA61AB2KXM",                // 统一社会信用代码

        // === 排口信息 ===
        "outlet_id": "DA001",                        // 排口ID（统一使用许可证排口ID）
        "pw_outlet_name": "1#废气排放口",             // 许可证排口名称
        "hp_outlet_name": "固化废气",                 // 环评排口名称

        // === 污染物信息 ===
        "pw_pollutant_name": "挥发性有机物",          // 许可证污染物名称
        "hp_pollutant_name": "VOCs（以非甲烷总烃计）", // 环评污染物名称
        "std_code": "a24088",                        // 污染物标准编码（统一使用许可证编码）
        "pollution_type": "organized",               // 污染类型：organized(有组织废气)

        // === 速率限值对比 ===
        "pw_emission_rate_limit": 17.0,              // 许可证排放速率限值
        "hp_emission_rate_limit": 8.5                // 环评排放速率限值
      }
    ]
  }
}

字段说明：
1. 所有ID和编码字段已去重，统一使用许可证数据中的值（因为关联逻辑相同）
2. 名称字段分别保留，以便区分许可证和环评中的不同表述
3. pollution_type: "organized"表示有组织废气，"water"表示废水
4. 限值字段根据检查类型不同：
   - total_emission_clues: pw_emission_value, hp_emission_value（年排放量）
   - concentration_clues: pw_concentration_limit, hp_concentration_limit（浓度限值）
   - emission_rate_clues: pw_emission_rate_limit, hp_emission_rate_limit（排放速率限值）
"""


if __name__ == "__main__":
    # 测试示例
    city = "九龙坡区"
    start_date = "2024-01-01"
    end_date = "2024-12-31"

    result = run(city, start_date, end_date)

    print(f"全厂排放口总计线索数量: {len(result['total_emission_clues'])}")
    print(f"排口浓度线索数量: {len(result['concentration_clues'])}")
    print(f"排口速率线索数量: {len(result['emission_rate_clues'])}")

    # 打印前几条线索作为示例
    if result['total_emission_clues']:
        print("\n全厂排放口总计线索示例:")
        for i, clue in enumerate(result['total_emission_clues'][:3]):
            print(f"  {i+1}. {clue}")

    if result['concentration_clues']:
        print("\n排口浓度线索示例:")
        for i, clue in enumerate(result['concentration_clues'][:3]):
            print(f"  {i+1}. {clue}")

    if result['emission_rate_clues']:
        print("\n排口速率线索示例:")
        for i, clue in enumerate(result['emission_rate_clues'][:3]):
            print(f"  {i+1}. {clue}")
